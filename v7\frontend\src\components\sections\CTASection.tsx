import { motion } from "framer-motion";
import { <PERSON>Zap, FiArrowRight } from "react-icons/fi";
import { <PERSON> } from "react-router-dom";
import { Button } from "../ui/Button";
import { AnimatedSection } from "../animations/index";

export function CTASection() {
  return (
    <AnimatedSection>
      <motion.section
        className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-2xl p-6 sm:p-8 mb-8 relative overflow-hidden"
        whileHover={{ scale: 1.01 }}
        transition={{ duration: 0.3 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-700/90" />
        <div className="relative z-10 max-w-3xl mx-auto text-center">
          <motion.h2
            className="text-2xl sm:text-3xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            Ready to modernize your legacy systems?
          </motion.h2>
          <motion.p
            className="text-lg sm:text-xl mb-8 text-indigo-100"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
          >
            Get started with SiliconAgent Transform today and see how our
            AI-powered platform can help you upgrade, migrate, and modernize
            your systems with minimal risk.
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/demo">
                <Button
                  size="lg"
                  className="bg-white text-indigo-600 hover:bg-indigo-50 hover:text-indigo-700 dark:bg-white dark:text-indigo-600 dark:hover:bg-indigo-50 dark:hover:text-indigo-700 shadow-lg hover:shadow-xl font-semibold"
                >
                  <FiZap className="mr-2" />
                  Try yourself
                  <FiArrowRight className="ml-2" />
                </Button>
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-indigo-600 font-semibold"
                >
                  Contact Sales
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    </AnimatedSection>
  );
}
