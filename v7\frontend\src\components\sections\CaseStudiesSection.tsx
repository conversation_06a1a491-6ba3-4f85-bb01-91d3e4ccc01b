import { motion } from "framer-motion";
import { Fi<PERSON>heckCircle, FiArrowRight } from "react-icons/fi";
import { Button } from "../ui/Button";
import { Card, CardContent } from "../ui/Card";
import {
  AnimatedSection,
  staggerContainer,
  scaleIn,
} from "../animations/index";

export function CaseStudiesSection() {
  const caseStudies = [
    {
      category: "Java Modernization",
      title: "Financial Services Firm Upgrades Legacy Java Platform",
      description:
        "Learn how we helped a major financial services company upgrade their Java 6 platform to Java 17, reducing operational costs by 60%.",
      metric: "4x",
      metricLabel: "performance boost",
    },
    {
      category: "UI Modernization",
      title: "Healthcare Provider Transforms Patient Portal",
      description:
        "See how we transformed a legacy healthcare patient portal into a modern, responsive application that improved patient satisfaction.",
      metric: "92%",
      metricLabel: "patient satisfaction",
    },
    {
      category: "Stack Transformation",
      title: "Retail Giant Moves to Microservices Architecture",
      description:
        "Discover how we helped a major retailer transform their monolithic e-commerce platform into a scalable microservices architecture.",
      metric: "300%",
      metricLabel: "scalability increase",
    },
  ];

  return (
    <AnimatedSection>
      <section className="case-studies mb-16">
        <motion.div
          className="flex items-center mb-6"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiCheckCircle className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Case Studies
          </h2>
        </motion.div>

        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Explore our case studies to see how we've helped enterprises modernize
          their systems, reduce costs, and accelerate innovation.
        </p>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          {caseStudies.map((study, index) => (
            <motion.div
              key={study.title}
              variants={scaleIn}
              whileHover={{ scale: 1.03, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="overflow-hidden hover:shadow-2xl transition-all duration-300 h-full">
                <div className="h-32 sm:h-40 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 relative overflow-hidden">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-500/20"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, 0],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <motion.div
                      className="text-4xl sm:text-5xl font-bold text-indigo-600/30 dark:text-indigo-400/30"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.5 + index * 0.2 }}
                    >
                      {study.metric}
                    </motion.div>
                  </div>
                </div>
                <CardContent className="p-4 sm:p-6">
                  <div className="inline-block bg-indigo-100 dark:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400 px-2 py-1 text-xs rounded mb-3">
                    {study.category}
                  </div>
                  <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white line-clamp-2">
                    {study.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm line-clamp-3">
                    {study.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-baseline">
                      <span className="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                        {study.metric}
                      </span>
                      <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                        {study.metricLabel}
                      </span>
                    </div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-indigo-600 border-indigo-600 hover:bg-indigo-600 hover:text-white dark:text-indigo-400 dark:border-indigo-400 dark:hover:bg-indigo-400 dark:hover:text-black text-xs"
                      >
                        Read Study
                      </Button>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <div className="text-center mt-8">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white">
              View All Case Studies <FiArrowRight className="ml-2" />
            </Button>
          </motion.div>
        </div>
      </section>
    </AnimatedSection>
  );
}
