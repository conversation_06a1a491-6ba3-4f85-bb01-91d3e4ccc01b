import { motion } from "framer-motion";
import { FiMessageSquare } from "react-icons/fi";
import {
  AnimatedSection,
  staggerContainer,
  fadeInUp,
} from "../animations/index";

export function FAQSection() {
  const faqs = [
    {
      question: "How long does a typical modernization project take?",
      answer:
        "Project timelines vary based on complexity and scope, but our AI-powered approach significantly reduces traditional timeframes. A typical Java upgrade might take 2-3 months, while a complete stack transformation could take 6-12 months. We work with you to establish realistic milestones and deliver value incrementally.",
    },
    {
      question: "How do you ensure business continuity during transformation?",
      answer:
        "We employ a parallel development approach with comprehensive testing at each stage. Our zero-downtime deployment strategy uses techniques like blue-green deployments and feature flags to ensure your systems remain operational throughout the transformation process.",
    },
    {
      question: "How do you preserve business logic during modernization?",
      answer:
        "Our AI-powered analysis tools extract and document existing business logic before any transformation begins. We use automated testing to ensure functional equivalence, and our transformation process prioritizes preserving business logic while upgrading the technical implementation.",
    },
    {
      question: "What level of involvement is required from our team?",
      answer:
        "While our platform reduces the technical lift, we do require collaboration with your subject matter experts to understand business requirements and validate transformations. We typically work with a small team of your developers, architects, and business analysts to ensure successful outcomes.",
    },
  ];

  return (
    <AnimatedSection>
      <section className="mb-16">
        <motion.div
          className="flex items-center mb-6"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiMessageSquare className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Frequently Asked Questions
          </h2>
        </motion.div>

        <motion.div
          className="space-y-4"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          {faqs.map((faq) => (
            <motion.div
              key={faq.question}
              className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
              variants={fadeInUp}
              whileHover={{ scale: 1.01, y: -2 }}
            >
              <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">
                {faq.question}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">{faq.answer}</p>
            </motion.div>
          ))}
        </motion.div>
      </section>
    </AnimatedSection>
  );
}
