import { motion } from "framer-motion";
import {
  <PERSON>R<PERSON>reshC<PERSON>,
  FiCode,
  FiCpu,
  FiShield,
  FiTrendingUp,
  FiZap,
  FiArrowRight,
} from "react-icons/fi";
import { Link } from "react-router-dom";
import { Button } from "../ui/Button";
import {
  fadeInLeft,
  fadeInRight,
  staggerContainer,
  scaleIn,
  FloatingElement,
  CleanParticleBackground,
} from "../animations/index";

export function HeroSection() {
  return (
    <motion.div
      className="pt-0 pb-20 relative overflow-hidden dark:bg-black transition-colors"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >
      <CleanParticleBackground />

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="flex flex-col xl:flex-row items-center min-h-[80vh] pt-8 gap-8">
          <motion.div className="w-full xl:w-1/2 mb-10 xl:mb-0" {...fadeInLeft}>
            <motion.span
              className="inline-block bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-300 font-medium rounded-full px-4 py-2 text-sm mb-6 backdrop-blur-sm border border-indigo-200 dark:border-indigo-800"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              whileHover={{ scale: 1.05 }}
            >
              ✨ Legacy System Modernization
            </motion.span>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 mb-8 leading-relaxed break-words pb-2">
                SiliconAgent Transform
              </h1>
            </motion.div>

            <motion.p
              className="text-lg sm:text-xl text-gray-700 dark:text-gray-300 mb-8 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              Upgrade, migrate, and modernize legacy systems with{" "}
              <span className="text-indigo-600 dark:text-indigo-400 font-semibold">
                AI precision
              </span>{" "}
              while preserving your valuable business logic.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link to="/demo">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
                  >
                    <FiZap className="mr-2" />
                    Try yourself
                    <FiArrowRight className="ml-2" />
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>

          <motion.div className="w-full xl:w-1/2" {...fadeInRight}>
            <FloatingElement>
              <motion.div
                className="relative max-w-2xl mx-auto"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="bg-white/80 dark:bg-black/80 backdrop-blur-xl shadow-2xl rounded-2xl p-8 sm:p-10 border border-gray-200/50 dark:border-gray-800/50">
                  <div className="flex items-center mb-8">
                    <motion.div
                      className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-4 sm:p-5 mr-4"
                      animate={{ rotate: [0, 360] }}
                      transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                    >
                      <FiRefreshCw className="text-white w-6 h-6 sm:w-7 sm:h-7" />
                    </motion.div>
                    <div>
                      <h3 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
                        AI-Powered Transformation
                      </h3>
                      <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400">
                        Modernize with precision and reliability
                      </p>
                    </div>
                  </div>
                  <motion.div
                    className="grid grid-cols-2 gap-4 sm:gap-6"
                    variants={staggerContainer}
                    initial="initial"
                    animate="animate"
                  >
                    {[
                      {
                        icon: FiCpu,
                        text: "AI Analysis",
                        color: "from-blue-500 to-cyan-500",
                      },
                      {
                        icon: FiCode,
                        text: "Code Transform",
                        color: "from-green-500 to-emerald-500",
                      },
                      {
                        icon: FiShield,
                        text: "Auto Testing",
                        color: "from-purple-500 to-pink-500",
                      },
                      {
                        icon: FiTrendingUp,
                        text: "Performance",
                        color: "from-orange-500 to-red-500",
                      },
                    ].map((item, index) => (
                      <motion.div
                        key={item.text}
                        className="bg-gray-50/80 dark:bg-gray-900/80 p-4 sm:p-5 rounded-xl border border-gray-200/50 dark:border-gray-700/50 flex items-center group hover:shadow-lg transition-all duration-300"
                        variants={scaleIn}
                        whileHover={{ scale: 1.05, y: -5 }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1 + index * 0.1, duration: 0.5 }}
                      >
                        <motion.div
                          className={`bg-gradient-to-r ${item.color} p-2 rounded-lg mr-4`}
                          whileHover={{ rotate: 10 }}
                        >
                          <item.icon className="text-white w-4 h-4 sm:w-5 sm:h-5" />
                        </motion.div>
                        <span className="text-sm sm:text-base font-medium group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                          {item.text}
                        </span>
                      </motion.div>
                    ))}
                  </motion.div>
                </div>

                {/* Subtle decorative elements */}
                <motion.div
                  className="absolute -top-6 -right-6 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-2xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute -bottom-6 -left-6 w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"
                  animate={{
                    scale: [1.2, 1, 1.2],
                    rotate: [360, 180, 0],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>
            </FloatingElement>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
