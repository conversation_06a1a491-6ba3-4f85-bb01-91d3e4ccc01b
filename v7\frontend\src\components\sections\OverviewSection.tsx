import { motion } from "framer-motion";
import { FiRefreshCw, FiCheck<PERSON>ircle } from "react-icons/fi";
import {
  AnimatedSection,
  fadeInUp,
  staggerContainer,
} from "../animations/index";

export function OverviewSection() {
  return (
    <AnimatedSection>
      <section className="overview mb-16">
        <motion.div
          className="flex items-center mb-6"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiRefreshCw className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Overview
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 xl:grid-cols-5 gap-8 mb-8">
          <motion.div
            className="xl:col-span-3"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
              SiliconAgent Transform is an enterprise platform designed to
              modernize legacy systems using AI-powered tools and techniques. We
              help you upgrade your technology stack while preserving your
              valuable business logic.
            </p>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
              Our approach combines AI analysis, automated code transformation,
              and rigorous testing to ensure a smooth transition with minimal
              risk. Whether you're upgrading Java applications, migrating from
              Struts to Spring, or transforming legacy UIs into modern web
              applications, our platform delivers consistent, reliable results.
            </p>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8"
              variants={staggerContainer}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              {[
                {
                  title: "60% Reduced Cost",
                  desc: "Lower modernization expenses",
                  icon: FiCheckCircle,
                },
                {
                  title: "Minimized Risk",
                  desc: "Automated testing and validation",
                  icon: FiCheckCircle,
                },
                {
                  title: "Preserved Logic",
                  desc: "Keep business logic while upgrading",
                  icon: FiCheckCircle,
                },
                {
                  title: "Zero Downtime",
                  desc: "Continuous operation during updates",
                  icon: FiCheckCircle,
                },
              ].map((item) => (
                <motion.div
                  key={item.title}
                  className="flex items-start group"
                  variants={fadeInUp}
                  whileHover={{ scale: 1.02, x: 5 }}
                >
                  <motion.div
                    className="rounded-full bg-green-100 dark:bg-green-900 p-2 mr-3 mt-1 group-hover:scale-110 transition-transform"
                    whileHover={{ rotate: 15 }}
                  >
                    <item.icon className="text-green-600 dark:text-green-400 w-4 h-4" />
                  </motion.div>
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {item.desc}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Transform Process */}
          <motion.div
            className="xl:col-span-2 bg-indigo-50 dark:bg-indigo-900/20 p-6 rounded-lg border border-indigo-100 dark:border-indigo-800"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
          >
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              The Transform Process
            </h3>
            <div className="space-y-4">
              {[
                {
                  step: "1",
                  title: "Analysis",
                  desc: "AI-powered code analysis identifies opportunities",
                },
                {
                  step: "2",
                  title: "Planning",
                  desc: "Detailed transformation plan with risk assessment",
                },
                {
                  step: "3",
                  title: "Transform",
                  desc: "Automated code transformation with oversight",
                },
                {
                  step: "4",
                  title: "Testing",
                  desc: "Comprehensive testing ensures equivalence",
                },
                {
                  step: "5",
                  title: "Deploy",
                  desc: "Zero-downtime deployment with rollback",
                },
              ].map((item, index) => (
                <motion.div
                  key={item.step}
                  className="flex"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  whileHover={{ x: 5 }}
                >
                  <motion.div
                    className="flex-shrink-0 w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center mr-3"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <span className="text-indigo-600 dark:text-indigo-400 font-bold text-sm">
                      {item.step}
                    </span>
                  </motion.div>
                  <div>
                    <h4 className="font-bold text-gray-900 dark:text-white text-sm">
                      {item.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {item.desc}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </AnimatedSection>
  );
}
