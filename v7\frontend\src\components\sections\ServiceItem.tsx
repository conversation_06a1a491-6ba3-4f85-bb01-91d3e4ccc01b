import { motion, AnimatePresence } from "framer-motion";
import { FiCheckCircle, FiArrowRight, FiMonitor } from "react-icons/fi";
import { Link } from "react-router-dom";
import { Button } from "../ui/Button";
import { TransformService } from "../../data/transformServices";

interface ServiceItemProps {
  service: TransformService;
  index: number;
  servicesData: TransformService[];
}

export function ServiceItem({
  service,
  index,
  servicesData,
}: ServiceItemProps) {
  return (
    <motion.div
      className="min-h-screen flex items-center justify-center relative"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: false, margin: "-20%" }}
      transition={{ duration: 0.8 }}
    >
      {/* Background Gradient */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r ${service.color} opacity-5 rounded-full blur-3xl`}
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Service Icon and Navigation */}
            <motion.div
              className="flex flex-col items-center lg:items-start"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {/* Large Icon */}
              <motion.div
                className={`w-32 h-32 rounded-3xl bg-gradient-to-r ${service.color} flex items-center justify-center mb-8 shadow-2xl`}
                whileHover={{ scale: 1.05, rotate: 5 }}
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <service.icon className="w-16 h-16 text-white" />
              </motion.div>

              {/* Service Title */}
              <motion.h3
                className="text-4xl sm:text-5xl font-bold text-center lg:text-left mb-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <span
                  className={`bg-gradient-to-r ${service.color} bg-clip-text text-transparent`}
                >
                  {service.title}
                </span>
              </motion.h3>

              {/* Progress Indicator */}
              <div className="flex space-x-2 mb-8">
                {servicesData.map((_, idx) => (
                  <motion.div
                    key={idx}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      idx === index
                        ? `bg-gradient-to-r ${service.color}`
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                    animate={{
                      scale: idx === index ? 1.2 : 1,
                    }}
                  />
                ))}
              </div>
            </motion.div>

            {/* Right side - Service Content */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Service-specific content */}
                  {service.id === "java" && (
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                          Upgrade Paths:
                        </h4>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                          {service.upgradePaths?.map((path, idx) => (
                            <motion.div
                              key={path}
                              className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg text-center font-medium border border-gray-200 dark:border-gray-700"
                              initial={{ opacity: 0, scale: 0.9 }}
                              whileInView={{
                                opacity: 1,
                                scale: 1,
                              }}
                              viewport={{ once: true }}
                              transition={{
                                delay: 0.6 + idx * 0.1,
                              }}
                              whileHover={{ scale: 1.05 }}
                            >
                              {path}
                            </motion.div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                          Key Features:
                        </h4>
                        <ul className="space-y-3">
                          {service.features?.map((feature, idx) => (
                            <motion.li
                              key={idx}
                              className="flex items-start"
                              initial={{ opacity: 0, x: -10 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{
                                delay: 0.8 + idx * 0.1,
                              }}
                            >
                              <FiCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-600 dark:text-gray-300">
                                {feature}
                              </span>
                            </motion.li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  {service.id === "framework" && (
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                          Common Migrations:
                        </h4>
                        <div className="space-y-4">
                          {service.migrations?.map((migration, idx) => (
                            <motion.div
                              key={`${migration.from}-${migration.to}`}
                              className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 flex items-center justify-between"
                              initial={{ opacity: 0, x: -20 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{
                                delay: 0.6 + idx * 0.2,
                              }}
                              whileHover={{ scale: 1.02 }}
                            >
                              <div className="flex items-center">
                                <div
                                  className={`w-8 h-8 ${migration.fromColor} rounded-lg mr-4`}
                                ></div>
                                <span className="font-medium">
                                  {migration.from}
                                </span>
                              </div>
                              <FiArrowRight className="text-indigo-500 mx-6 text-xl" />
                              <div className="flex items-center">
                                <div
                                  className={`w-8 h-8 ${migration.toColor} rounded-lg mr-4`}
                                ></div>
                                <span className="font-medium">
                                  {migration.to}
                                </span>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {service.id === "ui" && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiMonitor className="text-red-500 mr-2" />
                          Before
                        </h4>
                        <div className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 p-4 h-32 flex items-center justify-center">
                          <div className="text-center w-full">
                            <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                            <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                            <div className="w-3/4 mx-auto h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                            <div className="w-1/2 mx-auto h-4 bg-gray-400 dark:bg-gray-600 mt-3 rounded"></div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
                          Static, non-responsive legacy UI
                        </p>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiMonitor className="text-green-500 mr-2" />
                          After
                        </h4>
                        <div className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 p-4 h-32 flex items-center justify-center">
                          <div className="text-center w-full">
                            <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                            <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                            <div className="w-3/4 mx-auto h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                            <div className="w-1/2 mx-auto h-4 bg-indigo-500 dark:bg-indigo-500 mt-3 rounded-full"></div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
                          Modern, responsive UI for all devices
                        </p>
                      </div>
                    </div>
                  )}

                  {service.id === "api" && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {service.services?.map((apiService, idx) => (
                        <motion.div
                          key={apiService.title}
                          className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700"
                          initial={{ opacity: 0, scale: 0.9 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          viewport={{ once: true }}
                          transition={{ delay: 0.6 + idx * 0.1 }}
                          whileHover={{ scale: 1.02 }}
                        >
                          <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-3">
                            <apiService.icon className="text-indigo-500 mr-2 w-5 h-5" />
                            {apiService.title}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {apiService.desc}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  )}

                  {service.id === "stack" && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiMonitor className="text-red-500 mr-2" />
                          Legacy Stack
                        </h4>
                        <div className="space-y-3">
                          {service.comparison?.legacy.map((item) => (
                            <div
                              key={item}
                              className="flex items-center p-3 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
                            >
                              <span className="w-3 h-3 bg-red-200 dark:bg-red-900 rounded-full mr-3"></span>
                              <span className="text-sm">{item}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                        <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiMonitor className="text-green-500 mr-2" />
                          Modern Stack
                        </h4>
                        <div className="space-y-3">
                          {service.comparison?.modern.map((item) => (
                            <div
                              key={item}
                              className="flex items-center p-3 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
                            >
                              <span className="w-3 h-3 bg-green-200 dark:bg-green-900 rounded-full mr-3"></span>
                              <span className="text-sm">{item}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {service.id === "ai" && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {service.options?.map((option, idx) => (
                        <motion.div
                          key={option.title}
                          className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700"
                          initial={{ opacity: 0, scale: 0.9 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          viewport={{ once: true }}
                          transition={{ delay: 0.6 + idx * 0.1 }}
                          whileHover={{ scale: 1.02 }}
                        >
                          <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-3">
                            <option.icon className="text-indigo-500 mr-2 w-5 h-5" />
                            {option.title}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {option.desc}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  )}

                  <div className="pt-6">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: 1 }}
                    >
                      <Button
                        className={`bg-gradient-to-r ${service.color} text-white hover:shadow-lg transition-all duration-300`}
                      >
                        <Link to="/demo" className="flex items-center">
                          Learn More About {service.title}
                          <FiArrowRight className="ml-2" />
                        </Link>
                      </Button>
                    </motion.div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
