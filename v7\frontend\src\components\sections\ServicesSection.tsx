import { useState, useEffect, useRef } from "react";
import {
  motion,
  useScroll,
  useTransform,
  AnimatePresence,
} from "framer-motion";
import { FiServer } from "react-icons/fi";
import { AnimatedSection } from "../animations/index";
import { servicesData } from "../../data/transformServices";
import { ServiceContent } from "./ServiceContent";

export function ServicesSection() {
  const [activeService, setActiveService] = useState(0);
  const servicesRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: servicesRef,
    offset: ["start center", "end center"],
  });

  // Transform scroll progress to service index
  const serviceProgress = useTransform(
    scrollYProgress,
    [0, 1],
    [0, servicesData.length - 1]
  );

  useEffect(() => {
    const unsubscribe = serviceProgress.onChange((latest) => {
      const newIndex = Math.round(latest);
      if (
        newIndex !== activeService &&
        newIndex >= 0 &&
        newIndex < servicesData.length
      ) {
        setActiveService(newIndex);
      }
    });

    return unsubscribe;
  }, [serviceProgress, activeService]);

  const currentService = servicesData[activeService];

  return (
    <section className="services" ref={servicesRef}>
      <AnimatedSection>
        <motion.div
          className="flex items-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiServer className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Transformation Services
          </h2>
        </motion.div>
      </AnimatedSection>

      {/* Fixed Height Container for Sequential Services */}
      <div
        className="relative"
        style={{ height: `${servicesData.length * 100}vh` }}
      >
        <div className="sticky top-0 h-screen flex items-center justify-center">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                {/* Left side - Fixed Service Icon and Navigation */}
                <motion.div
                  className="flex flex-col items-center lg:items-start"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  {/* Large Icon - Changes with active service */}
                  <motion.div
                    className={`w-32 h-32 rounded-3xl bg-gradient-to-r ${currentService.color} flex items-center justify-center mb-8 shadow-2xl`}
                    animate={{
                      y: [0, -10, 0],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    key={currentService.id}
                  >
                    <currentService.icon className="w-16 h-16 text-white" />
                  </motion.div>

                  {/* Service Title - Changes with active service */}
                  <AnimatePresence mode="wait">
                    <motion.h3
                      key={currentService.id}
                      className="text-4xl sm:text-5xl font-bold text-center lg:text-left mb-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.6 }}
                    >
                      <span
                        className={`bg-gradient-to-r ${currentService.color} bg-clip-text text-transparent`}
                      >
                        {currentService.title}
                      </span>
                    </motion.h3>
                  </AnimatePresence>

                  {/* Progress Indicator - Fixed position, only dots change */}
                  <div className="flex space-x-2 mb-8">
                    {servicesData.map((_, idx) => (
                      <motion.div
                        key={idx}
                        className={`w-3 h-3 rounded-full transition-all duration-500 ${
                          idx === activeService
                            ? `bg-gradient-to-r ${currentService.color}`
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                        animate={{
                          scale: idx === activeService ? 1.2 : 1,
                        }}
                        transition={{ duration: 0.3 }}
                      />
                    ))}
                  </div>
                </motion.div>

                {/* Right side - Service Content */}
                <ServiceContent service={currentService} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
