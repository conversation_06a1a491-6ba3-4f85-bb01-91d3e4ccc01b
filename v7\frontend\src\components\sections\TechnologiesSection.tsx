import { motion } from "framer-motion";
import {
  FiLayers,
  FiCode,
  FiPackage,
  FiCloudLightning,
  FiCpu,
} from "react-icons/fi";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "../ui/Tabs";
import { AnimatedSection } from "../animations/index";

export function TechnologiesSection() {
  return (
    <AnimatedSection>
      <section className="technologies mb-16 pt-16">
        <motion.div
          className="flex items-center mb-6"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiLayers className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Technologies
          </h2>
        </motion.div>

        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          We leverage the latest technologies to ensure a smooth and efficient
          transformation process, combining AI-powered tools with
          industry-standard platforms.
        </p>

        <Tabs defaultValue="languages" className="w-full">
          <TabsList className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-8">
            <TabsTrigger value="languages" className="flex items-center gap-2">
              <FiCode className="w-5 h-5" />
              <span className="hidden sm:inline">Languages</span>
            </TabsTrigger>
            <TabsTrigger value="frameworks" className="flex items-center gap-2">
              <FiPackage className="w-5 h-5" />
              <span className="hidden sm:inline">Frameworks</span>
            </TabsTrigger>
            <TabsTrigger value="cloud" className="flex items-center gap-2">
              <FiCloudLightning className="w-5 h-5" />
              <span className="hidden sm:inline">Cloud & DevOps</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2">
              <FiCpu className="w-5 h-5" />
              <span className="hidden sm:inline">AI & ML</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="languages">
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {[
                "Java",
                "JavaScript",
                "TypeScript",
                "Python",
                "C#",
                "Go",
                "Kotlin",
                "Swift",
              ].map((lang, index) => (
                <motion.div
                  key={lang}
                  className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                    <FiCode className="text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <span className="font-medium text-sm">{lang}</span>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="frameworks">
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {[
                "Spring",
                "React",
                "Angular",
                "Vue",
                "Django",
                ".NET Core",
                "Express",
                "Rails",
              ].map((framework, index) => (
                <motion.div
                  key={framework}
                  className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                    <FiPackage className="text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <span className="font-medium text-sm">{framework}</span>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="cloud">
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {[
                "AWS",
                "Azure",
                "Google Cloud",
                "Kubernetes",
                "Docker",
                "Jenkins",
                "Terraform",
                "GitLab CI",
              ].map((tool, index) => (
                <motion.div
                  key={tool}
                  className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                    <FiCloudLightning className="text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <span className="font-medium text-sm">{tool}</span>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="ai">
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {[
                "TensorFlow",
                "PyTorch",
                "OpenAI API",
                "Hugging Face",
                "Scikit-learn",
                "NLP",
                "Computer Vision",
                "MLOps",
              ].map((tech, index) => (
                <motion.div
                  key={tech}
                  className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                    <FiCpu className="text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <span className="font-medium text-sm">{tech}</span>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      </section>
    </AnimatedSection>
  );
}
