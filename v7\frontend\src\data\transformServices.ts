import {
  FiCode,
  FiPackage,
  FiLayout,
  FiGlobe,
  FiLayers,
  FiCpu,
  FiZap,
  FiShield,
  FiTrendingUp,
  FiCloudLightning,
  FiMessageSquare,
  FiMonitor,
} from "react-icons/fi";

export interface ServiceMigration {
  from: string;
  to: string;
  fromColor: string;
  toColor: string;
}

export interface ServiceOption {
  icon: any;
  title: string;
  desc: string;
}

export interface ServiceComparison {
  legacy: string[];
  modern: string[];
}

export interface TransformService {
  id: string;
  icon: any;
  label: string;
  title: string;
  description: string;
  features?: string[];
  upgradePaths?: string[];
  migrations?: ServiceMigration[];
  services?: ServiceOption[];
  comparison?: ServiceComparison;
  options?: ServiceOption[];
  color: string;
}

export const servicesData: TransformService[] = [
  {
    id: "java",
    icon: FiCode,
    label: "Java Upgrades",
    title: "Java Upgrades",
    description:
      "Seamlessly upgrade your Java applications to the latest versions with automated code analysis, transformation, and testing. Our platform identifies deprecated APIs, language features, and patterns, then transforms them to modern equivalents.",
    features: [
      "Automated identification of deprecated APIs and language features",
      "Intelligent refactoring to use modern Java language features",
      "Comprehensive test coverage to ensure functional equivalence",
      "Detailed reporting and documentation of all changes",
    ],
    upgradePaths: [
      "Java 6 → Java 8+",
      "Java 8 → Java 11+",
      "Java 11 → Java 17+",
    ],
    color: "from-blue-500 to-cyan-500",
  },
  {
    id: "framework",
    icon: FiPackage,
    label: "Framework Migration",
    title: "Framework Migration",
    description:
      "Migrate from outdated frameworks to modern alternatives while preserving functionality and business logic. Our AI-powered platform analyzes your codebase, maps concepts between frameworks, and generates equivalent code.",
    migrations: [
      {
        from: "Struts",
        to: "Spring",
        fromColor: "bg-gray-300 dark:bg-gray-700",
        toColor: "bg-green-300 dark:bg-green-700",
      },
      {
        from: "Angular.js",
        to: "React",
        fromColor: "bg-red-300 dark:bg-red-700",
        toColor: "bg-blue-300 dark:bg-blue-700",
      },
    ],
    color: "from-green-500 to-emerald-500",
  },
  {
    id: "ui",
    icon: FiLayout,
    label: "UI Modernization",
    title: "UI Modernization",
    description:
      "Transform legacy UIs into responsive, modern web applications that work across all devices. Our platform analyzes your existing UI, extracts design patterns and business logic, then rebuilds it using modern frameworks.",
    color: "from-purple-500 to-pink-500",
  },
  {
    id: "api",
    icon: FiGlobe,
    label: "API Transformation",
    title: "API Transformation",
    description:
      "Modernize your APIs for better performance, scalability, and developer experience. Our platform can transform legacy SOAP services to REST/GraphQL, upgrade API versions, and implement modern security practices.",
    services: [
      {
        icon: FiZap,
        title: "SOAP to REST/GraphQL",
        desc: "Transform verbose SOAP services into modern, lightweight APIs",
      },
      {
        icon: FiShield,
        title: "Security Modernization",
        desc: "Upgrade to OAuth 2.0, JWT, and modern security standards",
      },
      {
        icon: FiTrendingUp,
        title: "Performance Optimization",
        desc: "Improve API performance with caching and pagination",
      },
      {
        icon: FiCode,
        title: "API Versioning",
        desc: "Implement proper versioning and backward compatibility",
      },
    ],
    color: "from-orange-500 to-red-500",
  },
  {
    id: "stack",
    icon: FiLayers,
    label: "Stack Transformation",
    title: "Stack Transformation",
    description:
      "Transform your entire legacy technology stack to a modern, cloud-native architecture. Our comprehensive approach ensures all components work together harmoniously while preserving business logic.",
    comparison: {
      legacy: [
        "Monolithic Architecture",
        "On-Premise Infrastructure",
        "Heavyweight Frameworks",
        "Manual Deployment",
      ],
      modern: [
        "Microservices Architecture",
        "Cloud-Native Infrastructure",
        "Lightweight Frameworks",
        "CI/CD Automation",
      ],
    },
    color: "from-cyan-500 to-blue-500",
  },
  {
    id: "ai",
    icon: FiCpu,
    label: "AI Integration",
    title: "AI Integration",
    description:
      "Introduce AI capabilities into your existing products and codebases without a complete rewrite. Our platform identifies opportunities for AI enhancement and implements them with minimal disruption.",
    options: [
      {
        icon: FiCloudLightning,
        title: "Predictive Analytics",
        desc: "Add forecasting and trend detection to existing data systems",
      },
      {
        icon: FiMessageSquare,
        title: "Conversational Interfaces",
        desc: "Integrate chatbots and natural language processing",
      },
      {
        icon: FiZap,
        title: "Intelligent Automation",
        desc: "Enhance existing workflows with AI-driven decision making",
      },
      {
        icon: FiMonitor,
        title: "Computer Vision",
        desc: "Add image recognition and processing capabilities",
      },
    ],
    color: "from-indigo-500 to-purple-500",
  },
];
