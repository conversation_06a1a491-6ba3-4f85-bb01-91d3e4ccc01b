import { BackToTop } from "../components/ui/BackToTop";
import { HeroSection } from "../components/sections/HeroSection";
import { OverviewSection } from "../components/sections/OverviewSection";
import { ServicesSection } from "../components/sections/ServicesSection";
import { TechnologiesSection } from "../components/sections/TechnologiesSection";
import { CaseStudiesSection } from "../components/sections/CaseStudiesSection";
import { CTASection } from "../components/sections/CTASection";
import { FAQSection } from "../components/sections/FAQSection";

// Set document title and meta description for this page
document.title = "SiliconAgent Transform - Legacy System Modernization";
const metaDescription = document.querySelector('meta[name="description"]');
if (metaDescription) {
  metaDescription.setAttribute(
    "content",
    "SiliconAgent Transform is an enterprise platform for modernizing legacy systems, including Java upgrades and UI modernization."
  );
} else {
  const meta = document.createElement("meta");
  meta.name = "description";
  meta.content =
    "SiliconAgent Transform is an enterprise platform for modernizing legacy systems, including Java upgrades and UI modernization.";
  document.head.appendChild(meta);
}

export default function Transform() {
  return (
    <div>
      {/* Hero Section */}
      <HeroSection />

      {/* Main Content */}
      <div className="product-page py-16 bg-white dark:bg-black overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6">
          {/* Overview Section */}
          <OverviewSection />

          {/* Services Section */}
          <ServicesSection />

          {/* Technologies Section */}
          <TechnologiesSection />

          {/* Case Studies Section */}
          <CaseStudiesSection />

          {/* Call to Action */}
          <CTASection />

          {/* FAQ Section */}
          <FAQSection />
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  );
}
            <AnimatedSection>
              <motion.div
                className="flex items-center mb-12"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiServer className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Transformation Services
                </h2>
              </motion.div>
            </AnimatedSection>

            {/* Sequential Service Sections */}
            <div className="space-y-32">
              {servicesData.map((service, index) => (
                <motion.div
                  key={service.id}
                  className="min-h-screen flex items-center justify-center relative"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: false, margin: "-20%" }}
                  transition={{ duration: 0.8 }}
                >
                  {/* Background Gradient */}
                  <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    <motion.div
                      className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r ${service.color} opacity-5 rounded-full blur-3xl`}
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 180, 360],
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                    />
                  </div>

                  <div className="container mx-auto px-4 sm:px-6 relative z-10">
                    <div className="max-w-6xl mx-auto">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        {/* Left side - Service Icon and Navigation */}
                        <motion.div
                          className="flex flex-col items-center lg:items-start"
                          initial={{ opacity: 0, x: -50 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.8, delay: 0.2 }}
                        >
                          {/* Large Icon */}
                          <motion.div
                            className={`w-32 h-32 rounded-3xl bg-gradient-to-r ${service.color} flex items-center justify-center mb-8 shadow-2xl`}
                            whileHover={{ scale: 1.05, rotate: 5 }}
                            animate={{
                              y: [0, -10, 0],
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          >
                            <service.icon className="w-16 h-16 text-white" />
                          </motion.div>

                          {/* Service Title */}
                          <motion.h3
                            className="text-4xl sm:text-5xl font-bold text-center lg:text-left mb-4"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                          >
                            <span
                              className={`bg-gradient-to-r ${service.color} bg-clip-text text-transparent`}
                            >
                              {service.title}
                            </span>
                          </motion.h3>

                          {/* Progress Indicator */}
                          <div className="flex space-x-2 mb-8">
                            {servicesData.map((_, idx) => (
                              <motion.div
                                key={idx}
                                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                  idx === index
                                    ? `bg-gradient-to-r ${service.color}`
                                    : "bg-gray-300 dark:bg-gray-600"
                                }`}
                                animate={{
                                  scale: idx === index ? 1.2 : 1,
                                }}
                              />
                            ))}
                          </div>
                        </motion.div>

                        {/* Right side - Service Content */}
                        <motion.div
                          className="space-y-6"
                          initial={{ opacity: 0, x: 50 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.8, delay: 0.4 }}
                        >
                          <AnimatePresence mode="wait">
                            <motion.div
                              key={service.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              transition={{ duration: 0.5 }}
                            >
                              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                                {service.description}
                              </p>

                              {/* Service-specific content */}
                              {service.id === "java" && (
                                <div className="space-y-6">
                                  <div>
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                                      Upgrade Paths:
                                    </h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                      {service.upgradePaths?.map(
                                        (path, idx) => (
                                          <motion.div
                                            key={path}
                                            className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg text-center font-medium border border-gray-200 dark:border-gray-700"
                                            initial={{ opacity: 0, scale: 0.9 }}
                                            whileInView={{
                                              opacity: 1,
                                              scale: 1,
                                            }}
                                            viewport={{ once: true }}
                                            transition={{
                                              delay: 0.6 + idx * 0.1,
                                            }}
                                            whileHover={{ scale: 1.05 }}
                                          >
                                            {path}
                                          </motion.div>
                                        )
                                      )}
                                    </div>
                                  </div>

                                  <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                                      Key Features:
                                    </h4>
                                    <ul className="space-y-3">
                                      {service.features?.map((feature, idx) => (
                                        <motion.li
                                          key={idx}
                                          className="flex items-start"
                                          initial={{ opacity: 0, x: -10 }}
                                          whileInView={{ opacity: 1, x: 0 }}
                                          viewport={{ once: true }}
                                          transition={{
                                            delay: 0.8 + idx * 0.1,
                                          }}
                                        >
                                          <FiCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0" />
                                          <span className="text-gray-600 dark:text-gray-300">
                                            {feature}
                                          </span>
                                        </motion.li>
                                      ))}
                                    </ul>
                                  </div>
                                </div>
                              )}

                              {service.id === "framework" && (
                                <div className="space-y-6">
                                  <div>
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 text-lg">
                                      Common Migrations:
                                    </h4>
                                    <div className="space-y-4">
                                      {service.migrations?.map(
                                        (migration, idx) => (
                                          <motion.div
                                            key={`${migration.from}-${migration.to}`}
                                            className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 flex items-center justify-between"
                                            initial={{ opacity: 0, x: -20 }}
                                            whileInView={{ opacity: 1, x: 0 }}
                                            viewport={{ once: true }}
                                            transition={{
                                              delay: 0.6 + idx * 0.2,
                                            }}
                                            whileHover={{ scale: 1.02 }}
                                          >
                                            <div className="flex items-center">
                                              <div
                                                className={`w-8 h-8 ${migration.fromColor} rounded-lg mr-4`}
                                              ></div>
                                              <span className="font-medium">
                                                {migration.from}
                                              </span>
                                            </div>
                                            <FiArrowRight className="text-indigo-500 mx-6 text-xl" />
                                            <div className="flex items-center">
                                              <div
                                                className={`w-8 h-8 ${migration.toColor} rounded-lg mr-4`}
                                              ></div>
                                              <span className="font-medium">
                                                {migration.to}
                                              </span>
                                            </div>
                                          </motion.div>
                                        )
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )}

                              {service.id === "ui" && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                  <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                                      <FiMonitor className="text-red-500 mr-2" />
                                      Before
                                    </h4>
                                    <div className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 p-4 h-32 flex items-center justify-center">
                                      <div className="text-center w-full">
                                        <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                                        <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                                        <div className="w-3/4 mx-auto h-2 bg-gray-300 dark:bg-gray-700 mb-2 rounded"></div>
                                        <div className="w-1/2 mx-auto h-4 bg-gray-400 dark:bg-gray-600 mt-3 rounded"></div>
                                      </div>
                                    </div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
                                      Static, non-responsive legacy UI
                                    </p>
                                  </div>

                                  <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                                      <FiMonitor className="text-green-500 mr-2" />
                                      After
                                    </h4>
                                    <div className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 p-4 h-32 flex items-center justify-center">
                                      <div className="text-center w-full">
                                        <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                                        <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                                        <div className="w-3/4 mx-auto h-2 bg-indigo-300 dark:bg-indigo-600 mb-2 rounded-full"></div>
                                        <div className="w-1/2 mx-auto h-4 bg-indigo-500 dark:bg-indigo-500 mt-3 rounded-full"></div>
                                      </div>
                                    </div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
                                      Modern, responsive UI for all devices
                                    </p>
                                  </div>
                                </div>
                              )}

                              {service.id === "api" && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                  {service.services?.map((apiService, idx) => (
                                    <motion.div
                                      key={apiService.title}
                                      className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700"
                                      initial={{ opacity: 0, scale: 0.9 }}
                                      whileInView={{ opacity: 1, scale: 1 }}
                                      viewport={{ once: true }}
                                      transition={{ delay: 0.6 + idx * 0.1 }}
                                      whileHover={{ scale: 1.02 }}
                                    >
                                      <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-3">
                                        <apiService.icon className="text-indigo-500 mr-2 w-5 h-5" />
                                        {apiService.title}
                                      </h5>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {apiService.desc}
                                      </p>
                                    </motion.div>
                                  ))}
                                </div>
                              )}

                              {service.id === "stack" && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                  <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                                      <FiServer className="text-red-500 mr-2" />
                                      Legacy Stack
                                    </h4>
                                    <div className="space-y-3">
                                      {service.comparison?.legacy.map(
                                        (item) => (
                                          <div
                                            key={item}
                                            className="flex items-center p-3 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
                                          >
                                            <span className="w-3 h-3 bg-red-200 dark:bg-red-900 rounded-full mr-3"></span>
                                            <span className="text-sm">
                                              {item}
                                            </span>
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </div>

                                  <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                                      <FiServer className="text-green-500 mr-2" />
                                      Modern Stack
                                    </h4>
                                    <div className="space-y-3">
                                      {service.comparison?.modern.map(
                                        (item) => (
                                          <div
                                            key={item}
                                            className="flex items-center p-3 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
                                          >
                                            <span className="w-3 h-3 bg-green-200 dark:bg-green-900 rounded-full mr-3"></span>
                                            <span className="text-sm">
                                              {item}
                                            </span>
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )}

                              {service.id === "ai" && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                  {service.options?.map((option, idx) => (
                                    <motion.div
                                      key={option.title}
                                      className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700"
                                      initial={{ opacity: 0, scale: 0.9 }}
                                      whileInView={{ opacity: 1, scale: 1 }}
                                      viewport={{ once: true }}
                                      transition={{ delay: 0.6 + idx * 0.1 }}
                                      whileHover={{ scale: 1.02 }}
                                    >
                                      <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-3">
                                        <option.icon className="text-indigo-500 mr-2 w-5 h-5" />
                                        {option.title}
                                      </h5>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {option.desc}
                                      </p>
                                    </motion.div>
                                  ))}
                                </div>
                              )}

                              <div className="pt-6">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  whileInView={{ opacity: 1, y: 0 }}
                                  viewport={{ once: true }}
                                  transition={{ delay: 1 }}
                                >
                                  <Button
                                    className={`bg-gradient-to-r ${service.color} text-white hover:shadow-lg transition-all duration-300`}
                                  >
                                    <Link
                                      to="/demo"
                                      className="flex items-center"
                                    >
                                      Learn More About {service.title}
                                      <FiArrowRight className="ml-2" />
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          </AnimatePresence>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </section>

          {/* Technologies Section */}
          <AnimatedSection>
            <section className="technologies mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiLayers className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Technologies
                </h2>
              </motion.div>

              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                We leverage the latest technologies to ensure a smooth and
                efficient transformation process, combining AI-powered tools
                with industry-standard platforms.
              </p>

              <Tabs defaultValue="languages" className="w-full">
                <TabsList className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-8">
                  <TabsTrigger
                    value="languages"
                    className="flex items-center gap-2"
                  >
                    <FiCode className="w-5 h-5" />
                    <span className="hidden sm:inline">Languages</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="frameworks"
                    className="flex items-center gap-2"
                  >
                    <FiPackage className="w-5 h-5" />
                    <span className="hidden sm:inline">Frameworks</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="cloud"
                    className="flex items-center gap-2"
                  >
                    <FiCloudLightning className="w-5 h-5" />
                    <span className="hidden sm:inline">Cloud & DevOps</span>
                  </TabsTrigger>
                  <TabsTrigger value="ai" className="flex items-center gap-2">
                    <FiCpu className="w-5 h-5" />
                    <span className="hidden sm:inline">AI & ML</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="languages">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "Java",
                      "JavaScript",
                      "TypeScript",
                      "Python",
                      "C#",
                      "Go",
                      "Kotlin",
                      "Swift",
                    ].map((lang, index) => (
                      <motion.div
                        key={lang}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCode className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{lang}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="frameworks">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "Spring",
                      "React",
                      "Angular",
                      "Vue",
                      "Django",
                      ".NET Core",
                      "Express",
                      "Rails",
                    ].map((framework, index) => (
                      <motion.div
                        key={framework}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiPackage className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{framework}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="cloud">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "AWS",
                      "Azure",
                      "Google Cloud",
                      "Kubernetes",
                      "Docker",
                      "Jenkins",
                      "Terraform",
                      "GitLab CI",
                    ].map((tool, index) => (
                      <motion.div
                        key={tool}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCloudLightning className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{tool}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="ai">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "TensorFlow",
                      "PyTorch",
                      "OpenAI API",
                      "Hugging Face",
                      "Scikit-learn",
                      "NLP",
                      "Computer Vision",
                      "MLOps",
                    ].map((tech, index) => (
                      <motion.div
                        key={tech}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCpu className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{tech}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>
              </Tabs>
            </section>
          </AnimatedSection>

          {/* Case Studies Section */}
          <AnimatedSection>
            <section className="case-studies mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiCheckCircle className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Case Studies
                </h2>
              </motion.div>

              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                Explore our case studies to see how we've helped enterprises
                modernize their systems, reduce costs, and accelerate
                innovation.
              </p>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-6"
                variants={staggerContainer}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
              >
                {[
                  {
                    category: "Java Modernization",
                    title:
                      "Financial Services Firm Upgrades Legacy Java Platform",
                    description:
                      "Learn how we helped a major financial services company upgrade their Java 6 platform to Java 17, reducing operational costs by 60%.",
                    metric: "4x",
                    metricLabel: "performance boost",
                  },
                  {
                    category: "UI Modernization",
                    title: "Healthcare Provider Transforms Patient Portal",
                    description:
                      "See how we transformed a legacy healthcare patient portal into a modern, responsive application that improved patient satisfaction.",
                    metric: "92%",
                    metricLabel: "patient satisfaction",
                  },
                  {
                    category: "Stack Transformation",
                    title: "Retail Giant Moves to Microservices Architecture",
                    description:
                      "Discover how we helped a major retailer transform their monolithic e-commerce platform into a scalable microservices architecture.",
                    metric: "300%",
                    metricLabel: "scalability increase",
                  },
                ].map((study, index) => (
                  <motion.div
                    key={study.title}
                    variants={scaleIn}
                    whileHover={{ scale: 1.03, y: -5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="overflow-hidden hover:shadow-2xl transition-all duration-300 h-full">
                      <div className="h-32 sm:h-40 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 relative overflow-hidden">
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-500/20"
                          animate={{
                            scale: [1, 1.1, 1],
                            rotate: [0, 5, 0],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <motion.div
                            className="text-4xl sm:text-5xl font-bold text-indigo-600/30 dark:text-indigo-400/30"
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.5 + index * 0.2 }}
                          >
                            {study.metric}
                          </motion.div>
                        </div>
                      </div>
                      <CardContent className="p-4 sm:p-6">
                        <div className="inline-block bg-indigo-100 dark:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400 px-2 py-1 text-xs rounded mb-3">
                          {study.category}
                        </div>
                        <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white line-clamp-2">
                          {study.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm line-clamp-3">
                          {study.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-baseline">
                            <span className="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                              {study.metric}
                            </span>
                            <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                              {study.metricLabel}
                            </span>
                          </div>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-indigo-600 border-indigo-600 hover:bg-indigo-600 hover:text-white dark:text-indigo-400 dark:border-indigo-400 dark:hover:bg-indigo-400 dark:hover:text-black text-xs"
                            >
                              Read Study
                            </Button>
                          </motion.div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>

              <div className="text-center mt-8">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white">
                    View All Case Studies <FiArrowRight className="ml-2" />
                  </Button>
                </motion.div>
              </div>
            </section>
          </AnimatedSection>

          {/* Call to Action */}
          <AnimatedSection>
            <motion.section
              className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-2xl p-6 sm:p-8 mb-8 relative overflow-hidden"
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-700/90" />
              <div className="relative z-10 max-w-3xl mx-auto text-center">
                <motion.h2
                  className="text-2xl sm:text-3xl font-bold mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                >
                  Ready to modernize your legacy systems?
                </motion.h2>
                <motion.p
                  className="text-lg sm:text-xl mb-8 text-indigo-100"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 }}
                >
                  Get started with SiliconAgent Transform today and see how our
                  AI-powered platform can help you upgrade, migrate, and
                  modernize your systems with minimal risk.
                </motion.p>
                <motion.div
                  className="flex flex-col sm:flex-row justify-center gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.4 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link to="/demo">
                      <Button
                        size="lg"
                        className="bg-white text-indigo-600 hover:bg-indigo-50 hover:text-indigo-700 dark:bg-white dark:text-indigo-600 dark:hover:bg-indigo-50 dark:hover:text-indigo-700 shadow-lg hover:shadow-xl font-semibold"
                      >
                        <FiZap className="mr-2" />
                        Try yourself
                        <FiArrowRight className="ml-2" />
                      </Button>
                    </Link>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link to="/contact">
                      <Button
                        size="lg"
                        variant="outline"
                        className="border-2 border-white text-white hover:bg-white hover:text-indigo-600 font-semibold"
                      >
                        Contact Sales
                      </Button>
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
            </motion.section>
          </AnimatedSection>

          {/* FAQ Section */}
          <AnimatedSection>
            <section className="mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiMessageSquare className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Frequently Asked Questions
                </h2>
              </motion.div>

              <motion.div
                className="space-y-4"
                variants={staggerContainer}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
              >
                {[
                  {
                    question:
                      "How long does a typical modernization project take?",
                    answer:
                      "Project timelines vary based on complexity and scope, but our AI-powered approach significantly reduces traditional timeframes. A typical Java upgrade might take 2-3 months, while a complete stack transformation could take 6-12 months. We work with you to establish realistic milestones and deliver value incrementally.",
                  },
                  {
                    question:
                      "How do you ensure business continuity during transformation?",
                    answer:
                      "We employ a parallel development approach with comprehensive testing at each stage. Our zero-downtime deployment strategy uses techniques like blue-green deployments and feature flags to ensure your systems remain operational throughout the transformation process.",
                  },
                  {
                    question:
                      "How do you preserve business logic during modernization?",
                    answer:
                      "Our AI-powered analysis tools extract and document existing business logic before any transformation begins. We use automated testing to ensure functional equivalence, and our transformation process prioritizes preserving business logic while upgrading the technical implementation.",
                  },
                  {
                    question:
                      "What level of involvement is required from our team?",
                    answer:
                      "While our platform reduces the technical lift, we do require collaboration with your subject matter experts to understand business requirements and validate transformations. We typically work with a small team of your developers, architects, and business analysts to ensure successful outcomes.",
                  },
                ].map((faq) => (
                  <motion.div
                    key={faq.question}
                    className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
                    variants={fadeInUp}
                    whileHover={{ scale: 1.01, y: -2 }}
                  >
                    <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">
                      {faq.question}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {faq.answer}
                    </p>
                  </motion.div>
                ))}
              </motion.div>
            </section>
          </AnimatedSection>
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  );
}
