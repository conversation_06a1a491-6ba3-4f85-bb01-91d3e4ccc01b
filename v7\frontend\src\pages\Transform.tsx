import { BackToTop } from "../components/ui/BackToTop";
import { HeroSection } from "../components/sections/HeroSection";
import { OverviewSection } from "../components/sections/OverviewSection";
import { ServicesSection } from "../components/sections/ServicesSection";
import { TechnologiesSection } from "../components/sections/TechnologiesSection";
import { CaseStudiesSection } from "../components/sections/CaseStudiesSection";
import { CTASection } from "../components/sections/CTASection";
import { FAQSection } from "../components/sections/FAQSection";

// Set document title and meta description for this page
document.title = "SiliconAgent Transform - Legacy System Modernization";
const metaDescription = document.querySelector('meta[name="description"]');
if (metaDescription) {
  metaDescription.setAttribute(
    "content",
    "SiliconAgent Transform is an enterprise platform for modernizing legacy systems, including Java upgrades and UI modernization."
  );
} else {
  const meta = document.createElement("meta");
  meta.name = "description";
  meta.content =
    "SiliconAgent Transform is an enterprise platform for modernizing legacy systems, including Java upgrades and UI modernization.";
  document.head.appendChild(meta);
}

export default function Transform() {
  return (
    <div>
      {/* Hero Section */}
      <HeroSection />

      {/* Main Content */}
      <div className="product-page py-16 bg-white dark:bg-black overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6">
          {/* Overview Section */}
          <OverviewSection />

          {/* Services Section */}
          <ServicesSection />

          {/* Technologies Section */}
          <TechnologiesSection />

          {/* Case Studies Section */}
          <CaseStudiesSection />

          {/* Call to Action */}
          <CTASection />

          {/* FAQ Section */}
          <FAQSection />
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  );
}
